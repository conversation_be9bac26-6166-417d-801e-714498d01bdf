<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Chạy migration để tạo bảng orders
     * Bảng này lưu thông tin đơn hàng của users
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $blueprintTable) {
            $blueprintTable->id();

            // Foreign key tới bảng users, cascade delete khi user bị xóa
            $blueprintTable->foreignId('user_id')
                ->constrained('users')
                ->cascadeOnDelete();

            // Gi<PERSON> trị đơn hàng (bigInteger để lưu số lớn)
            $blueprintTable->bigInteger('price');

            // Ngày tạo đơn hàng
            $blueprintTable->date('date');

            // Ghi chú cho đơn hàng (có thể null)
            $blueprintTable->string('note')->nullable();

            // Timestamps tự động (created_at, updated_at)
            $blueprintTable->timestamps();
        });
    }

    /**
     * Rollback migration - xóa bảng orders
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
