<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thống kê doanh thu</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f9fafb;
        }

        h2 {
            margin-bottom: 15px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background: #f3f4f6;
        }

        .ok {
            color: green;
            font-weight: bold;
        }

        .fail {
            color: red;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <h2>Thống kê doanh thu</h2>
    <p>Tổng doanh thu hệ thống: <strong><?php echo e(number_format($total)); ?></strong> VND</p>
    <p>Tổng quỹ thưởng: <strong><?php echo e(number_format($bonusPool)); ?></strong> VND</p>
    <p>Thưởng mỗi NPP đạt chuẩn: <strong><?php echo e(number_format($bonusEach)); ?></strong> VND</p>

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Tên NPP</th>
                <th>Doanh thu cá nhân</th>
                <th>Danh hiệu</th>
                <th>Thưởng</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $u): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><?php echo e($u['id']); ?></td>
                    <td><?php echo e($u['name']); ?></td>
                    <td><?php echo e(number_format($u['total_price'])); ?> VND</td>
                    <td class="<?php echo e($u['title'] == 'Đạt chuẩn' ? 'ok' : 'fail'); ?>">
                        <?php echo e($u['title']); ?>

                    </td>
                    <td>
                        <?php echo e($u['title'] == 'Đạt chuẩn' ? number_format($bonusEach) . ' VND' : '-'); ?>

                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\example1\resources\views/order/statistics.blade.php ENDPATH**/ ?>