<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserRelationship extends Model
{
    use HasFactory;

    /**
     * Tên bảng trong database
     */
    protected $table = 'user_relationship';

    /**
     * Primary key của bảng
     */
    protected $primaryKey = 'id';

    /**
     * <PERSON><PERSON>c thuộc tính có thể mass assignable
     */
    protected $fillable = [
        'user_id',
        'user_id_parent',
    ];

    /**
     * <PERSON>uan hệ nhiều-một với bảng users (user con)
     * Một relationship thuộc về một user
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * <PERSON>uan hệ nhiều-một với bảng users (user cha)
     * Một relationship thuộc về một user parent
     */
    public function parentUser()
    {
        return $this->belongsTo(User::class, 'user_id_parent', 'id');
    }
}
