<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;

    /**
     * Tên bảng trong database
     */
    protected $table = 'orders';

    /**
     * Primary key của bảng
     */
    protected $primaryKey = 'id';

    /**
     * <PERSON><PERSON><PERSON> thuộc tính có thể mass assignable
     */
    protected $fillable = [
        'user_id',
        'price',
        'note',
        'date',
    ];

    /**
     * <PERSON><PERSON><PERSON> thuộc tính sẽ được cast sang kiểu dữ liệu khác
     */
    protected $casts = [
        'price' => 'decimal:2',
        'date' => 'date',
    ];

    /**
     * <PERSON>uan hệ nhiều-một với bảng users
     * Một order thuộc về một user
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
