<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crawl <PERSON>ội Dung Bà<PERSON>t</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 300;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        input[type="url"] {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        input[type="url"]:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .error {
            background: #fee;
            color: #c33;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #c33;
        }

        .examples {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .examples h3 {
            color: #555;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .example-urls {
            list-style: none;
        }

        .example-urls li {
            margin-bottom: 8px;
        }

        .example-urls a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            cursor: pointer;
        }

        .example-urls a:hover {
            text-decoration: underline;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🕷️ Crawl Bài Viết</h1>

        @if ($errors->any())
            <div class="error">
                {{ $errors->first() }}
            </div>
        @endif

        <form action="{{ route('crawl.process') }}" method="POST" id="crawlForm">
            @csrf
            <div class="form-group">
                <label for="url">Nhập URL bài viết cần crawl:</label>
                <input type="url" id="url" name="url" placeholder="https://example.com/bai-viet"
                    value="{{ old('url') }}" required>
            </div>

            <button type="submit" class="btn">
                <span class="btn-text">🚀 Bắt đầu Crawl</span>
            </button>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Đang crawl nội dung, vui lòng đợi...</p>
            </div>
        </form>
    </div>

    <script>
        function fillUrl(url) {
            document.getElementById('url').value = url;
        }

        document.getElementById('crawlForm').addEventListener('submit', function() {
            document.querySelector('.btn-text').style.display = 'none';
            document.getElementById('loading').style.display = 'block';
            document.querySelector('.btn').disabled = true;
        });
    </script>
</body>

</html>
