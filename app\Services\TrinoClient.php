<?php

namespace App\Services;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;

class TrinoClient
{
    // Hằng số mặc định cho cấu hình
    private const DEFAULT_HOST = 'localhost';
    private const DEFAULT_PORT = 8080;
    private const DEFAULT_USER = 'laravel';
    private const DEFAULT_CATALOG = 'tpch';
    private const DEFAULT_SCHEMA = 'tiny';
    private const DEFAULT_TIMEOUT = 60;
    private const DEFAULT_MAX_PAGES = 500;

    private string $baseUrl;
    private string $userName;
    private string $catalogName;
    private string $schemaName;
    private ?string $basicAuthUser;
    private ?string $basicAuthPassword;
    private bool $useTls;
    private int $timeoutSeconds;
    private int $maxPagesLimit;

    /**
     * Khởi tạo Trino client với cấu hình từ config file
     */
    public function __construct()
    {
        $hostName = config('trino.host', self::DEFAULT_HOST);
        $portNumber = (int) config('trino.port', self::DEFAULT_PORT);
        $this->useTls = (bool) config('trino.tls', false);
        $this->baseUrl = ($this->useTls ? 'https' : 'http') . "://{$hostName}:{$portNumber}";
        $this->userName = config('trino.user', self::DEFAULT_USER);
        $this->catalogName = config('trino.catalog', self::DEFAULT_CATALOG);
        $this->schemaName = config('trino.schema', self::DEFAULT_SCHEMA);
        $this->basicAuthUser = config('trino.basic_user');
        $this->basicAuthPassword = config('trino.basic_pass');
        $this->timeoutSeconds = (int) config('trino.timeout', self::DEFAULT_TIMEOUT);
        $this->maxPagesLimit = (int) config('trino.max_pages', self::DEFAULT_MAX_PAGES);
    }

    /**
     * Tạo HTTP client mặc định với các header Trino cần thiết
     */
    protected function createHttpClient(): PendingRequest
    {
        $httpRequest = Http::timeout($this->timeoutSeconds)
            ->withHeaders([
                'X-Trino-User' => $this->userName,
                'X-Trino-Catalog' => $this->catalogName,
                'X-Trino-Schema' => $this->schemaName,
            ]);

        if ($this->basicAuthUser && $this->basicAuthPassword) {
            $httpRequest = $httpRequest->withBasicAuth($this->basicAuthUser, $this->basicAuthPassword);
        }

        return $httpRequest;
    }

    /**
     * Chạy SQL query và trả về mảng các dòng kết quả theo tên cột
     *
     * @param string $sqlQuery Câu lệnh SQL cần thực thi
     * @param array $sessionProperties Mảng session properties
     * @param array $extraHeaders Header bổ sung nếu cần
     * @return array Mảng kết quả với key là tên cột
     */
    public function query(string $sqlQuery, array $sessionProperties = [], array $extraHeaders = []): array
    {
        $requestHeaders = $extraHeaders;

        // Thêm session properties dưới dạng header X-Trino-Session
        if (!empty($sessionProperties)) {
            $sessionPairs = [];
            foreach ($sessionProperties as $propertyKey => $propertyValue) {
                $sessionPairs[] = "{$propertyKey}={$propertyValue}";
            }
            $requestHeaders['X-Trino-Session'] = implode(',', $sessionPairs);
        }

        // POST câu lệnh SQL tới Trino
        $response = $this->createHttpClient()
            ->withHeaders($requestHeaders)
            ->withBody($sqlQuery, 'text/plain')
            ->post("{$this->baseUrl}/v1/statement");

        if (!$response->successful()) {
            throw new \RuntimeException("Trino POST failed ({$response->status()}): " . $response->body());
        }

        $responseData = $response->json();
        $resultRows = [];
        $columnSchema = $responseData['columns'] ?? null;

        // Poll nextUri cho đến khi hoàn tất
        $currentPage = 0;
        while (true) {
            if (!empty($responseData['error'])) {
                $errorMessage = $responseData['error']['message'] ?? 'Unknown Trino error';
                $errorCode = $responseData['error']['errorCode'] ?? null;
                throw new \RuntimeException("Trino error: {$errorMessage}" . ($errorCode ? " (code {$errorCode})" : ''));
            }

            if (!empty($responseData['data'])) {
                $resultRows = array_merge($resultRows, $responseData['data']);
            }

            if (empty($responseData['nextUri'])) {
                break; // Đã hoàn tất
            }

            if (++$currentPage > $this->maxPagesLimit) {
                throw new \RuntimeException("Trino polling exceeded max pages ({$this->maxPagesLimit}).");
            }

            $response = $this->createHttpClient()->get($responseData['nextUri']);
            if (!$response->successful()) {
                throw new \RuntimeException("Trino GET nextUri failed ({$response->status()}): " . $response->body());
            }
            $responseData = $response->json();

            // Lưu schema cột nếu xuất hiện muộn
            if (!$columnSchema && !empty($responseData['columns'])) {
                $columnSchema = $responseData['columns'];
            }
        }

        // Map hàng thành mảng kết hợp theo tên cột
        if ($columnSchema) {
            $columnNames = array_map(fn($column) => $column['name'], $columnSchema);
            $resultRows = array_map(function ($row) use ($columnNames) {
                // Đảm bảo cùng độ dài với số cột
                $row = array_pad($row, count($columnNames), null);
                return array_combine($columnNames, $row);
            }, $resultRows);
        }

        return $resultRows;
    }

    /**
     * Chạy SQL query và trả về raw payload JSON cuối cùng
     * Dành cho trường hợp muốn tự xử lý response
     *
     * @param string $sqlQuery Câu lệnh SQL cần thực thi
     * @param array $sessionProperties Mảng session properties
     * @param array $extraHeaders Header bổ sung nếu cần
     * @return array Raw JSON response từ Trino
     */
    public function queryRaw(string $sqlQuery, array $sessionProperties = [], array $extraHeaders = []): array
    {
        $requestHeaders = $extraHeaders;

        if (!empty($sessionProperties)) {
            $sessionPairs = [];
            foreach ($sessionProperties as $propertyKey => $propertyValue) {
                $sessionPairs[] = "{$propertyKey}={$propertyValue}";
            }
            $requestHeaders['X-Trino-Session'] = implode(',', $sessionPairs);
        }

        $response = $this->createHttpClient()
            ->withHeaders($requestHeaders)
            ->withBody($sqlQuery, 'text/plain')
            ->post("{$this->baseUrl}/v1/statement");

        if (!$response->successful()) {
            throw new \RuntimeException("Trino POST failed ({$response->status()}): " . $response->body());
        }

        $responseData = $response->json();
        $finalResponse = $responseData;

        $currentPage = 0;
        while (!empty($responseData['nextUri'])) {
            if (++$currentPage > $this->maxPagesLimit) {
                throw new \RuntimeException("Trino polling exceeded max pages ({$this->maxPagesLimit}).");
            }

            $response = $this->createHttpClient()->get($responseData['nextUri']);
            if (!$response->successful()) {
                throw new \RuntimeException("Trino GET nextUri failed ({$response->status()}): " . $response->body());
            }

            $responseData = $response->json();
            $finalResponse = $responseData ?: $finalResponse;
        }

        return $finalResponse ?? [];
    }
}
