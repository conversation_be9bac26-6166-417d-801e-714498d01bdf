<?php
namespace App\Services;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;

class TrinoClient
{
    private string $base;
    private string $user;
    private string $catalog;
    private string $schema;
    private ?string $basicUser;
    private ?string $basicPass;
    private bool $tls;
    private int $timeout;  // giây
    private int $maxPages; // giới hạn số lần poll nextUri để an toàn

    public function __construct()
    {
        $host            = config('trino.host', 'localhost');
        $port            = (int) config('trino.port', 8080);
        $this->tls       = (bool) config('trino.tls', false);
        $this->base      = ($this->tls ? 'https' : 'http') . "://{$host}:{$port}";
        $this->user      = config('trino.user', 'laravel');
        $this->catalog   = config('trino.catalog', 'tpch');
        $this->schema    = config('trino.schema', 'tiny');
        $this->basicUser = config('trino.basic_user');
        $this->basicPass = config('trino.basic_pass');
        $this->timeout   = (int) config('trino.timeout', 60);
        $this->maxPages  = (int) config('trino.max_pages', 500);
    }

    /**
     * Http client mặc định kèm các header Trino.
     */
    protected function client(): PendingRequest
    {
        $req = Http::timeout($this->timeout)
            ->withHeaders([
                'X-Trino-User'    => $this->user,
                'X-Trino-Catalog' => $this->catalog,
                'X-Trino-Schema'  => $this->schema,
            ]);

        if ($this->basicUser && $this->basicPass) {
            $req = $req->withBasicAuth($this->basicUser, $this->basicPass);
        }

        return $req;
    }

    /**
     * Chạy SQL và trả về mảng các dòng (assoc) theo tên cột.
     * - $sessionProps: mảng session properties (vd ['join_distribution_type' => 'AUTOMATIC'])
     * - $extraHeaders: header bổ sung nếu cần
     */
    public function query(string $sql, array $sessionProps = [], array $extraHeaders = []): array
    {
        $headers = $extraHeaders;

        // Thêm session properties dưới dạng header X-Trino-Session
        // (mỗi key=value; phân tách bằng dấu phẩy)
        if (! empty($sessionProps)) {
            $pairs = [];
            foreach ($sessionProps as $k => $v) {
                $pairs[] = "{$k}={$v}";
            }
            $headers['X-Trino-Session'] = implode(',', $pairs);
        }

        // POST câu lệnh SQL
        $resp = $this->client()
            ->withHeaders($headers)
            ->withBody($sql, 'text/plain') // quan trọng: Trino nhận raw SQL
            ->post("{$this->base}/v1/statement");

        if (! $resp->successful()) {
            throw new \RuntimeException("Trino POST failed ({$resp->status()}): " . $resp->body());
        }

        $data    = $resp->json();
        $rows    = [];
        $columns = $data['columns'] ?? null;

        // Poll nextUri cho đến khi hoàn tất
        $page = 0;
        while (true) {
            if (! empty($data['error'])) {
                $message   = $data['error']['message'] ?? 'Unknown Trino error';
                $errorCode = $data['error']['errorCode'] ?? null;
                throw new \RuntimeException("Trino error: {$message}" . ($errorCode ? " (code {$errorCode})" : ''));
            }

            if (! empty($data['data'])) {
                // Ghép dữ liệu
                $rows = array_merge($rows, $data['data']);
            }

            if (empty($data['nextUri'])) {
                break; // đã hoàn tất
            }

            if (++$page > $this->maxPages) {
                throw new \RuntimeException("Trino polling exceeded max pages ({$this->maxPages}).");
            }

            $resp = $this->client()->get($data['nextUri']);
            if (! $resp->successful()) {
                throw new \RuntimeException("Trino GET nextUri failed ({$resp->status()}): " . $resp->body());
            }
            $data = $resp->json();

            // Lưu schema cột nếu xuất hiện muộn
            if (! $columns && ! empty($data['columns'])) {
                $columns = $data['columns'];
            }
        }

        // Map hàng → mảng kết hợp theo tên cột
        if ($columns) {
            $names = array_map(fn($c) => $c['name'], $columns);
            $rows  = array_map(function ($r) use ($names) {
                // một số connector trả null/thiếu cột; đảm bảo cùng độ dài
                $r = array_pad($r, count($names), null);
                return array_combine($names, $r);
            }, $rows);
        }

        return $rows;
    }

    /**
     * Chạy SQL và trả nguyên payload JSON cuối cùng (nếu bạn muốn tự xử lý).
     */
    public function queryRaw(string $sql, array $sessionProps = [], array $extraHeaders = []): array
    {
        $headers = $extraHeaders;

        if (! empty($sessionProps)) {
            $pairs = [];
            foreach ($sessionProps as $k => $v) {
                $pairs[] = "{$k}={$v}";
            }
            $headers['X-Trino-Session'] = implode(',', $pairs);
        }

        $resp = $this->client()
            ->withHeaders($headers)
            ->withBody($sql, 'text/plain')
            ->post("{$this->base}/v1/statement");

        if (! $resp->successful()) {
            throw new \RuntimeException("Trino POST failed ({$resp->status()}): " . $resp->body());
        }

        $data  = $resp->json();
        $final = $data;

        $page = 0;
        while (! empty($data['nextUri'])) {
            if (++$page > $this->maxPages) {
                throw new \RuntimeException("Trino polling exceeded max pages ({$this->maxPages}).");
            }
            $resp = $this->client()->get($data['nextUri']);
            if (! $resp->successful()) {
                throw new \RuntimeException("Trino GET nextUri failed ({$resp->status()}): " . $resp->body());
            }
            $data  = $resp->json();
            $final = $data ?: $final;
        }

        return $final ?? [];
    }
}
