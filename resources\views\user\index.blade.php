<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Cây NPP & Do<PERSON>h thu</title>
    <style>
        :root {
            --bg: #ffffff;
            --text: #1f2937;
            --muted: #6b7280;
            --line: #e5e7eb;
            --ring: #d1d5db;
            --card: #ffffff;
            --chip: #f3f4f6;
            --accent: #16a34a;
            --warn: #f59e0b;
            --btn: #f8fafc;
            --btnb: #d1d5db;
        }

        * {
            box-sizing: border-box
        }

        body {
            margin: 0;
            background: var(--bg);
            color: var(--text);
            font-family: system-ui, -apple-system, Segoe UI, Roboto, Inter, Helvetica, Arial
        }

        .container {
            max-width: 1100px;
            margin: 24px auto;
            padding: 0 16px
        }

        h2 {
            margin: 0 0 12px;
            font-size: 24px;
            font-weight: 700
        }

        .toolbar {
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
            background: #fff;
            border: 1px solid var(--ring);
            border-radius: 10px;
            padding: 10px 12px;
            margin: 10px 0 12px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, .05)
        }

        .toolbar input[type="search"] {
            flex: 1;
            min-width: 220px;
            background: #fff;
            border: 1px solid var(--ring);
            border-radius: 8px;
            padding: 9px 11px;
            color: var(--text);
            outline: none
        }

        .btn {
            border: 1px solid var(--btnb);
            background: var(--btn);
            color: var(--text);
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600
        }

        .btn:hover {
            background: #eef2f7
        }

        .result-info {
            font-size: 13px;
            color: var(--muted);
            margin: 6px 2px 12px
        }

        .tree {
            list-style: none;
            padding-left: 0;
            margin: 0;
            border-left: 1px dashed var(--line)
        }

        .tree>li {
            margin-left: 0;
            border-left: none
        }

        .tree ul {
            list-style: none;
            margin: 6px 0 0 18px;
            padding-left: 18px;
            border-left: 1px dashed var(--line)
        }

        li.collapsed>ul {
            display: none
        }

        .node {
            position: relative;
            margin: 8px 0
        }

        .node::before {
            content: "";
            position: absolute;
            left: -18px;
            top: 20px;
            width: 18px;
            height: 1px;
            background: var(--line)
        }

        .card {
            background: var(--card);
            border: 1px solid var(--ring);
            border-radius: 10px;
            padding: 8px 10px;
            display: flex;
            gap: 10px;
            align-items: center;
            box-shadow: 0 1px 2px rgba(0, 0, 0, .04)
        }

        .toggle {
            width: 26px;
            height: 26px;
            border: 1px solid var(--ring);
            border-radius: 7px;
            background: #fff;
            color: var(--text);
            cursor: pointer;
            line-height: 24px;
            text-align: center;
            font-size: 14px;
            user-select: none
        }

        .toggle.is-leaf {
            opacity: .35;
            pointer-events: none
        }

        .meta {
            flex: 1;
            min-width: 0
        }

        .name {
            font-weight: 700;
            font-size: 14.5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis
        }

        .kpis {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-top: 2px
        }

        .chip {
            background: var(--chip);
            border: 1px solid var(--ring);
            border-radius: 999px;
            padding: 4px 8px;
            font-size: 12.5px;
            color: #111827;
            display: inline-flex;
            gap: 6px;
            align-items: center
        }

        .chip .pill {
            font-weight: 800;
            color: #fff;
            padding: 1px 6px;
            border-radius: 999px
        }

        .pill.ok {
            background: var(--accent)
        }

        .pill.low {
            background: var(--warn)
        }

        .id {
            color: var(--muted);
            font-size: 12px
        }

        @media (max-width:600px) {
            .kpis {
                flex-direction: column;
                align-items: flex-start
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <h2>Cây NPP & Doanh thu</h2>

        <div class="toolbar">
            <input id="searchBox" type="search" placeholder="Tìm theo tên… (Enter)">
            <button id="expandAll" class="btn">Expand all</button>
            <button id="collapseAll" class="btn">Collapse all</button>
            <span id="resultInfo" class="result-info"></span>
        </div>

        @php $personalPrice = 5_000_000; @endphp

        @once
            @php
                // Closure render, dùng array access để tránh lỗi property trên array
                $renderNode = function ($id, $users, $children, $personal, $subtree, $personalPrice) use (
                    &$renderNode,
                ) {
                    if (!isset($users[$id])) {
                        return;
                    }

                    $user = $users[$id]; // $user là array: ['id'=>..., 'name'=>..., 'parent_id'=>...]
                    $p = (int) ($personal[$id] ?? 0);
                    $g = (int) ($subtree[$id] ?? 0);
                    $okClass = $p >= $personalPrice ? 'ok' : 'low';
                    $hasKids = !empty($children[$id]);

                    echo '<li class="' .
                        ($hasKids ? '' : 'leaf') .
                        '" data-name="' .
                        e(strtolower($user['name'])) .
                        '">';
                    echo '<div class="node"><div class="card">';
                    echo '<div class="toggle ' .
                        ($hasKids ? '' : 'is-leaf') .
                        '" title="' .
                        ($hasKids ? 'Mở/đóng nhánh' : 'Không có nhánh') .
                        '" onclick="toggleNode(this)">' .
                        ($hasKids ? '▾' : '•') .
                        '</div>';
                    echo '<div class="meta">';
                    echo '<div class="name">' . e($user['name']) . '</div>';
                    echo '<div class="kpis">';
                    echo '<span class="chip">Cá nhân: <span class="pill ' .
                        $okClass .
                        '">' .
                        number_format($p) .
                        '</span></span>';
                    echo '<span class="chip">Nhóm: <span class="pill" style="background:#9ca3af;color:#fff">' .
                        number_format($g) .
                        '</span></span>';
                    echo '<span class="id">#' .
                        $user['id'] .
                        ($user['parent_id'] ? ' · P:' . $user['parent_id'] : ' · F0') .
                        '</span>';
                    echo '</div>';
                    echo '</div>';
                    echo '</div></div>';

                    if ($hasKids) {
                        echo '<ul>';
                        foreach ($children[$id] as $cid) {
                            $renderNode($cid, $users, $children, $personal, $subtree, $personalPrice);
                        }
                        echo '</ul>';
                    }
                    echo '</li>';
                };
            @endphp
        @endonce

        @php
            // Lấy rootId an toàn nếu $root là array hoặc object
            $rootIdToRender = null;
            if (isset($root)) {
                if (is_array($root) && isset($root['id'])) {
                    $rootIdToRender = $root['id'];
                } elseif (is_object($root) && isset($root->id)) {
                    $rootIdToRender = $root->id;
                }
            }
        @endphp

        <ul class="tree" id="treeRoot">
            @php
                if ($rootIdToRender !== null) {
                    $renderNode($rootIdToRender, $users, $children, $personal, $subtree, $personalPrice);
                } else {
                    echo '<li><em>Không tìm thấy root</em></li>';
                }
            @endphp
        </ul>
    </div>

    <script>
        // toggle 1 node
        function toggleNode(btn) {
            const li = btn.closest('li');
            if (!li) return;
            li.classList.toggle('collapsed');
            btn.textContent = li.classList.contains('collapsed') ? '▸' : '▾';
        }

        const expandAllBtn = document.getElementById('expandAll');
        const collapseAllBtn = document.getElementById('collapseAll');
        const treeRoot = document.getElementById('treeRoot');
        const searchBox = document.getElementById('searchBox');
        const resultInfo = document.getElementById('resultInfo');

        if (expandAllBtn && treeRoot) {
            expandAllBtn.addEventListener('click', () => {
                treeRoot.querySelectorAll('li.collapsed').forEach(li => {
                    li.classList.remove('collapsed');
                    const t = li.querySelector('.toggle');
                    if (t) t.textContent = '▾';
                });
            });
        }

        if (collapseAllBtn && treeRoot) {
            collapseAllBtn.addEventListener('click', () => {
                treeRoot.querySelectorAll('li').forEach(li => {
                    if (li.querySelector('ul')) {
                        li.classList.add('collapsed');
                        const t = li.querySelector('.toggle');
                        if (t) t.textContent = '▸';
                    }
                });
                // luôn mở F0
                const f0 = treeRoot.querySelector(':scope > li');
                if (f0) {
                    f0.classList.remove('collapsed');
                    const t = f0.querySelector('.toggle');
                    if (t) t.textContent = '▾';
                }
            });
        }

        function applySearch(term) {
            term = (term || '').trim().toLowerCase();
            const lis = Array.from(treeRoot.querySelectorAll('li'));
            if (!term) {
                lis.forEach(li => li.style.display = '');
                if (resultInfo) resultInfo.textContent = '';
                return;
            }
            lis.forEach(li => li.style.display = 'none');

            let matchCount = 0;
            lis.forEach(li => {
                const name = li.getAttribute('data-name') || '';
                if (name.includes(term)) {
                    matchCount++;
                    let cur = li;
                    while (cur && cur !== treeRoot) {
                        cur.style.display = '';
                        if (cur.tagName === 'LI') {
                            cur.classList.remove('collapsed');
                            const t = cur.querySelector('.toggle');
                            if (t) t.textContent = '▾';
                        }
                        cur = cur.parentElement;
                        if (cur && cur.tagName === 'UL') cur.style.display = '';
                        cur = cur ? cur.parentElement : null;
                    }
                }
            });
            if (resultInfo) resultInfo.textContent = matchCount ? `Tìm thấy ${matchCount} kết quả` :
                'Không có kết quả phù hợp';
        }

        if (searchBox) {
            searchBox.addEventListener('keyup', e => {
                if (e.key === 'Enter') applySearch(e.target.value);
            });
        }
    </script>
</body>

</html>
