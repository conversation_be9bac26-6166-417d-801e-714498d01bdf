<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Chạy migration để tạo bảng users
     * Bảng này lưu thông tin người dùng với cấu trúc cây (parent-child)
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $blueprintTable) {
            $blueprintTable->id();

            // Foreign key tự tham chiếu để tạo cấu trúc cây
            $blueprintTable->foreignId('parent_id')
                ->nullable()
                ->constrained('users')
                ->nullOnDelete();

            // Tên người dùng
            $blueprintTable->string('name');

            // Email duy nhất
            $blueprintTable->string('email')->unique();

            // Thời gian xác thực email
            $blueprintTable->timestamp('email_verified_at')->nullable();

            // M<PERSON>t khẩu đã hash
            $blueprintTable->string('password');

            // Token để remember login
            $blueprintTable->rememberToken();

            // Timestamps tự động
            $blueprintTable->timestamps();
        });
    }

    /**
     * Rollback migration - xóa bảng users
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
