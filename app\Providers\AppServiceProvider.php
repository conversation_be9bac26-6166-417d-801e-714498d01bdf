<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

/**
 * Service Provider ch<PERSON>h của ứng dụng
 * Đăng ký và khởi tạo các service cần thiết cho app
 */
class AppServiceProvider extends ServiceProvider
{
    /**
     * Đăng ký các service vào container
     * Method này được gọi trước boot() và dùng để bind services
     */
    public function register(): void
    {
        // Đăng ký các service binding tại đây
    }

    /**
     * Bootstrap các service sau khi tất cả đã được đăng ký
     * Method này được gọi sau register() và dùng để cấu hình services
     */
    public function boot(): void
    {
        // Cấu hình các service sau khi đã được đăng ký
    }
}
