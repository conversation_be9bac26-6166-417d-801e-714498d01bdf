<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kết <PERSON> Crawl - {{ $articleData['title'] }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .back-btn {
            display: inline-block;
            margin: 20px 0;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .back-btn:hover {
            background: #5a6fd8;
        }
        
        .article-wrapper {
            background: white;
            margin: 20px auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .article-meta {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .meta-item {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .meta-label {
            font-weight: bold;
            color: #495057;
            min-width: 120px;
            margin-right: 10px;
        }
        
        .meta-value {
            color: #6c757d;
            word-break: break-all;
        }
        
        .article-title {
            font-size: 2.5em;
            font-weight: 700;
            color: #2c3e50;
            margin: 30px 0;
            line-height: 1.2;
            text-align: center;
        }
        
        .article-content {
            padding: 30px;
            font-size: 1.1em;
            line-height: 1.8;
        }
        
        .article-content h1,
        .article-content h2,
        .article-content h3,
        .article-content h4,
        .article-content h5,
        .article-content h6 {
            margin: 25px 0 15px 0;
            color: #2c3e50;
        }
        
        .article-content p {
            margin-bottom: 20px;
            text-align: justify;
        }
        
        .article-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .article-content ul,
        .article-content ol {
            margin: 20px 0;
            padding-left: 30px;
        }
        
        .article-content li {
            margin-bottom: 8px;
        }
        
        .article-content blockquote {
            border-left: 4px solid #667eea;
            padding: 15px 20px;
            margin: 20px 0;
            background: #f8f9fa;
            font-style: italic;
        }
        
        .article-content a {
            color: #667eea;
            text-decoration: none;
        }
        
        .article-content a:hover {
            text-decoration: underline;
        }
        
        .images-section {
            margin-top: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        
        .images-title {
            font-size: 1.5em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .image-item {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .image-item:hover {
            transform: translateY(-5px);
        }
        
        .image-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .image-caption {
            padding: 15px;
            font-size: 0.9em;
            color: #6c757d;
        }
        
        .no-content {
            text-align: center;
            padding: 50px;
            color: #6c757d;
            font-style: italic;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            background: #e9ecef;
            padding: 20px;
            margin-top: 20px;
            border-radius: 8px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .article-title {
                font-size: 1.8em;
            }
            
            .article-content {
                padding: 20px;
                font-size: 1em;
            }
            
            .meta-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .meta-label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>🎯 Kết Quả Crawl Thành Công</h1>
        </div>
    </div>
    
    <div class="container">
        <a href="{{ route('crawl.index') }}" class="back-btn">← Crawl URL khác</a>
        
        <div class="article-wrapper">
            <!-- Meta Information -->
            <div class="article-meta">
                <div class="meta-item">
                    <span class="meta-label">🌐 URL gốc:</span>
                    <span class="meta-value">
                        <a href="{{ $url }}" target="_blank">{{ $url }}</a>
                    </span>
                </div>
            </div>
            
            <!-- Article Title -->
            <h1 class="article-title">{{ $articleData['title'] }}</h1>
            
            <!-- Article Content -->
            <div class="article-content">
                @if($articleData['content'])
                    {!! $articleData['content'] !!}
                @else
                    <div class="no-content">
                        Không thể trích xuất nội dung từ bài viết này.
                    </div>
                @endif
            </div>
            
            <!-- Statistics -->
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">{{ str_word_count(strip_tags($articleData['content'])) }}</div>
                    <div class="stat-label">Số từ</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ count($articleData['images']) }}</div>
                    <div class="stat-label">Hình ảnh</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ strlen(strip_tags($articleData['content'])) }}</div>
                    <div class="stat-label">Ký tự</div>
                </div>
            </div>
            
            <!-- Images Section -->
            @if(count($articleData['images']) > 0)
            <div class="images-section">
                <h2 class="images-title">🖼️ Hình ảnh trong bài viết ({{ count($articleData['images']) }} ảnh)</h2>
                <div class="images-grid">
                    @foreach($articleData['images'] as $image)
                    <div class="image-item">
                        <img src="{{ $image['src'] }}" alt="{{ $image['alt'] }}" loading="lazy">
                        @if($image['alt'])
                        <div class="image-caption">{{ $image['alt'] }}</div>
                        @endif
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </div>
</body>
</html>
