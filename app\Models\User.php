<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * C<PERSON>c thuộc tính có thể mass assignable
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'parent_id',
    ];

    /**
     * <PERSON><PERSON><PERSON> thuộc tính sẽ bị ẩn khi serialize
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * <PERSON><PERSON><PERSON> thuộc tính sẽ được cast sang kiểu dữ liệu khác
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * <PERSON>uan hệ một-nhiều với bảng orders
     * Một user có thể có nhiều orders
     */
    public function orders()
    {
        return $this->hasMany(Order::class, 'user_id', 'id');
    }

    /**
     * <PERSON><PERSON> hệ một-nhiều với chính bảng users (self-referencing)
     * Một user có thể có nhiều user con (children)
     */
    public function children()
    {
        return $this->hasMany(User::class, 'parent_id', 'id');
    }

    /**
     * Quan hệ nhiều-một với chính bảng users (self-referencing)
     * Một user có thể có một user cha (parent)
     */
    public function parent()
    {
        return $this->belongsTo(User::class, 'parent_id', 'id');
    }
}
