<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OrderSeeder extends Seeder
{
    // Hằng số định nghĩa số lượng records và batch size
    private const TOTAL_RECORDS = 1_000_000;
    private const BATCH_SIZE = 5000;
    private const MIN_PRICE = 10000;
    private const MAX_PRICE = 1000000;
    private const SENTENCE_WORDS = 6;

    /**
     * Run the database seeds.
     * Tạo dữ liệu mẫu cho bảng orders với 1 triệu records
     */
    public function run(): void
    {
        // Lấy danh sách user IDs từ database
        $userIds = DB::table('users')->pluck('id')->toArray();

        // Khởi tạo Faker với locale Việt Nam
        $faker = \Faker\Factory::create('vi_VN');

        // Xử lý theo batch để tránh memory overflow
        for ($currentIndex = 0; $currentIndex < self::TOTAL_RECORDS; $currentIndex += self::BATCH_SIZE) {
            $orderRows = [];

            // Tạo dữ liệu cho từng batch
            for ($batchIndex = 0; $batchIndex < self::BATCH_SIZE; $batchIndex++) {
                $orderRows[] = [
                    'user_id' => $faker->randomElement($userIds),
                    'price' => $faker->numberBetween(self::MIN_PRICE, self::MAX_PRICE),
                    'note' => $faker->sentence(self::SENTENCE_WORDS),
                    'date' => $faker->dateTimeBetween('-6 months', 'now')->format('Y-m-d'),
                ];
            }

            // Insert batch vào database
            DB::table('orders')->insert($orderRows);

            // Hiển thị tiến trình
            $processedRecords = $currentIndex + self::BATCH_SIZE;
            echo "Inserted: {$processedRecords} / " . self::TOTAL_RECORDS . "\n";
        }
    }
}
