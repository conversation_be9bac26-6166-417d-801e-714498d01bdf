<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $userIds = DB::table('users')->pluck('id')->toArray();
        $total   = 1_000_000; // 1 triệu bản ghi
        $batch   = 5000;      // chèn theo batch 5000 bản ghi 1 lần

        $faker = \Faker\Factory::create('vi_VN');

        for ($i = 0; $i < $total; $i += $batch) {
            $rows = [];
            for ($j = 0; $j < $batch; $j++) {
                $rows[] = [
                    'user_id' => $faker->randomElement($userIds),
                    'price'   => $faker->numberBetween(10000, 1000000), // 10k - 1tr
                    'note'    => $faker->sentence(6),
                    'date'    => $faker->dateTimeBetween('-6 months', 'now')
                        ->format('Y-m-d'),
                ];
            }
            DB::table('orders')->insert($rows);

            echo "Inserted: " . ($i + $batch) . " / $total\n";
        }
    }
}
