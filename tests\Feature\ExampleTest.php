<?php

namespace Tests\Feature;

use Tests\TestCase;

/**
 * Feature test example - test tích hợp các chức năng của ứng dụng
 * Kiểm tra các endpoint và flow hoàn chỉnh
 */
class ExampleTest extends TestCase
{
    /**
     * Test cơ bản kiểm tra ứng dụng trả về response thành công
     * Gửi GET request tới root path và expect status 200
     */
    public function testApplicationReturnsSuccessfulResponse(): void
    {
        $httpResponse = $this->get('/');

        $httpResponse->assertStatus(200);
    }
}
