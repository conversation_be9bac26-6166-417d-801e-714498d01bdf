<?php

namespace Tests;

use Illuminate\Contracts\Console\Kernel;
use Illuminate\Foundation\Application;

/**
 * Trait để tạo Laravel application instance cho testing
 * Đ<PERSON><PERSON><PERSON> sử dụng bởi TestCase để khởi tạo app trong môi trường test
 */
trait CreatesApplication
{
    /**
     * Tạo và khởi tạo Laravel application instance
     * Bootstrap kernel và trả về app instance để sử dụng trong tests
     */
    public function createApplication(): Application
    {
        $applicationInstance = require __DIR__ . '/../bootstrap/app.php';

        $applicationInstance->make(Kernel::class)->bootstrap();

        return $applicationInstance;
    }
}
