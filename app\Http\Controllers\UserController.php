<?php
namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\TrinoClient;

class UserController extends Controller
{
    public function index(TrinoClient $trino, $rootId = 1)
    {
        // 1) Lấy danh sách users (id, name, parent_id) từ Trino
        //    => KHÔNG recursive, chỉ scan 1 lần
        $usersSql = <<<SQL
            SELECT
                u.id,
                u.name,
                u.parent_id
            FROM mysql.example1.users u
        SQL;

        $users = collect($trino->query($usersSql));

        // 2) L<PERSON>y doanh thu cá nhân (pre-aggregate) từ Trino
        //    ép kiểu price sang DOUBLE để SUM
        $personalSql = <<<SQL
            SELECT
                o.user_id AS id,
                COALESCE(SUM(CAST(o.price AS DOUBLE)), 0) AS personal_vnd
            FROM mysql.example1.orders o
            GROUP BY o.user_id
        SQL;

        $personalRows = collect($trino->query($personalSql));
        $personal     = $personalRows->pluck('personal_vnd', 'id')->all();

        // 3) Xây dựng children map cho cây (từ toàn bộ users)
        $children = [];
        $userById = [];
        foreach ($users as $u) {
            $uid            = (int) $u['id'];
            $pid            = $u['parent_id'] !== null ? (int) $u['parent_id'] : null;
            $userById[$uid] = $u;
            if (! isset($children[$uid])) {
                $children[$uid] = [];
            }

            if ($pid !== null) {
                $children[$pid][] = $uid;
            }
        }
        // Đảm bảo key tồn tại
        if (! isset($children[$rootId])) {
            $children[$rootId] = [];
        }

        // 4) Tính doanh thu nhóm (subtree) bằng DFS trên PHP
        //    (rất nhanh vì đã có personal_vnd pre-aggregate)
        $subtree = [];
        $visited = [];

        $dfs = function (int $id) use (&$dfs, &$subtree, &$visited, $children, $personal): float {
            if (isset($visited[$id])) {
                return $subtree[$id];
            }

            $sum = (float) ($personal[$id] ?? 0);
            foreach ($children[$id] ?? [] as $cid) {
                $sum += $dfs($cid);
            }
            $visited[$id]        = true;
            return $subtree[$id] = $sum;
        };

        // Nếu bạn chỉ muốn tính trong subtree của $rootId để giảm tải:
        $stack     = [$rootId];
        $reachable = [];
        while ($stack) {
            $cur = array_pop($stack);
            if (isset($reachable[$cur])) {
                continue;
            }

            $reachable[$cur] = true;
            foreach ($children[$cur] ?? [] as $cid) {
                $stack[] = $cid;
            }
        }
        // Tính DFS chỉ cho các node reachable (tiết kiệm thời gian/memory)
        foreach (array_keys($reachable) as $uid) {
            $dfs($uid);
        }

        // 5) Trả về view
        return view('user.index', [
            'root'     => $userById[$rootId] ?? null,
            'users'    => collect($userById), // keyBy id
            'children' => $children,
            'personal' => $personal, // [id => personal_vnd]
            'subtree'  => $subtree,  // [id => group_vnd]
        ]);
    }
}
