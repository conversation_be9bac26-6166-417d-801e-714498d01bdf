<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\TrinoClient;

class UserController extends Controller
{
    private const DEFAULT_ROOT_ID = 1;

    /**
     * Hi<PERSON>n thị danh sách users với cấu trúc cây và doanh thu
     * Sử dụng Trino để query dữ liệu và DFS để tính toán subtree revenue
     */
    public function index(TrinoClient $trinoClient, $rootId = self::DEFAULT_ROOT_ID)
    {
        // Lấy danh sách users từ Trino - không recursive, chỉ scan 1 lần
        $usersSqlQuery = <<<SQL
            SELECT
                u.id,
                u.name,
                u.parent_id
            FROM mysql.example1.users u
        SQL;

        $usersCollection = collect($trinoClient->query($usersSqlQuery));

        // Lấy doanh thu cá nhân pre-aggregate từ Trino
        $personalRevenueSql = <<<SQL
            SELECT
                o.user_id AS id,
                COALESCE(SUM(CAST(o.price AS DOUBLE)), 0) AS personal_vnd
            FROM mysql.example1.orders o
            GROUP BY o.user_id
        SQL;

        $personalRevenueRows = collect($trinoClient->query($personalRevenueSql));
        $personalRevenueMap = $personalRevenueRows->pluck('personal_vnd', 'id')->all();

        // Xây dựng children map cho cây từ toàn bộ users
        $childrenMap = [];
        $userByIdMap = [];

        foreach ($usersCollection as $userRow) {
            $userId = (int) $userRow['id'];
            $parentId = $userRow['parent_id'] !== null ? (int) $userRow['parent_id'] : null;

            $userByIdMap[$userId] = $userRow;

            if (!isset($childrenMap[$userId])) {
                $childrenMap[$userId] = [];
            }

            if ($parentId !== null) {
                $childrenMap[$parentId][] = $userId;
            }
        }

        // Đảm bảo root key tồn tại
        if (!isset($childrenMap[$rootId])) {
            $childrenMap[$rootId] = [];
        }

        // Tính doanh thu nhóm subtree bằng DFS trên PHP
        $subtreeRevenueMap = [];
        $visitedNodesMap = [];

        $calculateSubtreeRevenue = function (int $nodeId) use (
            &$calculateSubtreeRevenue,
            &$subtreeRevenueMap,
            &$visitedNodesMap,
            $childrenMap,
            $personalRevenueMap
        ): float {
            if (isset($visitedNodesMap[$nodeId])) {
                return $subtreeRevenueMap[$nodeId];
            }

            $totalRevenue = (float) ($personalRevenueMap[$nodeId] ?? 0);

            foreach ($childrenMap[$nodeId] ?? [] as $childId) {
                $totalRevenue += $calculateSubtreeRevenue($childId);
            }

            $visitedNodesMap[$nodeId] = true;
            return $subtreeRevenueMap[$nodeId] = $totalRevenue;
        };

        // Tìm các node reachable từ rootId để giảm tải tính toán
        $nodeStack = [$rootId];
        $reachableNodesMap = [];

        while ($nodeStack) {
            $currentNode = array_pop($nodeStack);

            if (isset($reachableNodesMap[$currentNode])) {
                continue;
            }

            $reachableNodesMap[$currentNode] = true;

            foreach ($childrenMap[$currentNode] ?? [] as $childId) {
                $nodeStack[] = $childId;
            }
        }

        // Tính DFS chỉ cho các node reachable
        foreach (array_keys($reachableNodesMap) as $userId) {
            $calculateSubtreeRevenue($userId);
        }

        // Trả về view với dữ liệu đã xử lý
        return view('user.index', [
            'root' => $userByIdMap[$rootId] ?? null,
            'users' => collect($userByIdMap),
            'children' => $childrenMap,
            'personal' => $personalRevenueMap,
            'subtree' => $subtreeRevenueMap,
        ]);
    }
}
