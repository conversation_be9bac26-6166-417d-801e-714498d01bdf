<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\TrinoClient;

class OrderController extends Controller
{
    // Số doanh thu 1 tháng tối thiểu của NPP
    private const MIN_USER_PRICE = 5000000;
    // Số doanh thu 1 tháng tối thiểu của nhóm
    private const MIN_GROUP_PRICE = 250000000;
    // Tỉ lệ thưởng
    private const DISCOUNT_RATE = 0.01;

    private const STATUS_QUALIFIED = 'Đạt chuẩn';
    private const STATUS_NOT_QUALIFIED = 'Chưa đạt';

    /**
     * Thống kê tổng doanh số và phân bổ thưởng
     * Sử dụng Trino để query dữ liệu từ database
     */
    public function statistics(TrinoClient $trinoClient)
    {
        // Tính toán các mốc thời gian: tháng hiện tại và 2 tháng trước
        $currentMonth = now()->startOfMonth();
        $previousMonth1 = now()->copy()->subMonth()->startOfMonth();
        $previousMonth2 = now()->copy()->subMonths(2)->startOfMonth();

        $monthsArray = [
            $currentMonth->format('Y-m'),  // T
            $previousMonth1->format('Y-m'), // T-1
            $previousMonth2->format('Y-m'), // T-2
        ];

        // Lấy tổng doanh thu toàn hệ thống trong tháng hiện tại
        $totalRevenueSql = <<<SQL
            SELECT COALESCE(SUM(CAST(price AS DOUBLE)), 0) AS total
            FROM mysql.example1.orders
            WHERE date BETWEEN DATE '{$currentMonth->toDateString()}'
            AND DATE '{$currentMonth->copy()->endOfMonth()->toDateString()}'
        SQL;

        $totalRevenue = (float) ($trinoClient->query($totalRevenueSql)[0]['total'] ?? 0);

        // Lấy doanh thu cá nhân theo từng tháng trong 3 tháng gần nhất
        $monthlyRevenueSql = <<<SQL
            SELECT user_id,
            date_format(CAST(date AS timestamp), '%Y-%m') AS ym,
            SUM(CAST(price AS DOUBLE)) AS total
            FROM mysql.example1.orders
            WHERE date BETWEEN DATE '{$previousMonth2->toDateString()}'
            AND DATE '{$currentMonth->copy()->endOfMonth()->toDateString()}'
            GROUP BY user_id, date_format(CAST(date AS timestamp), '%Y-%m')
        SQL;

        $monthlyRevenueRows = $trinoClient->query($monthlyRevenueSql);

        // Tạo map doanh thu theo user và tháng
        $monthlyRevenueMap = [];
        foreach ($monthlyRevenueRows as $revenueRow) {
            $userId = (int) $revenueRow['user_id'];
            $yearMonth = (string) $revenueRow['ym'];
            $monthlyRevenueMap[$userId][$yearMonth] = (float) $revenueRow['total'];
        }

        // Lấy danh sách tất cả users
        $userListSql = "SELECT id, name FROM mysql.example1.users";
        $userRows = $trinoClient->query($userListSql);

        // Xử lý dữ liệu user và kiểm tra điều kiện đạt chuẩn
        $usersCollection = collect($userRows)->map(function (array $userRow) use ($monthsArray, $monthlyRevenueMap) {
            $userId = (int) $userRow['id'];

            // Lấy doanh thu 3 tháng gần nhất
            $currentMonthRevenue = $monthlyRevenueMap[$userId][$monthsArray[0]] ?? 0.0;
            $previousMonth1Revenue = $monthlyRevenueMap[$userId][$monthsArray[1]] ?? 0.0;
            $previousMonth2Revenue = $monthlyRevenueMap[$userId][$monthsArray[2]] ?? 0.0;

            // Kiểm tra điều kiện đạt chuẩn: 3 tháng liên tiếp >= MIN_USER_PRICE
            $isQualified = ($currentMonthRevenue >= self::MIN_USER_PRICE)
                && ($previousMonth1Revenue >= self::MIN_USER_PRICE)
                && ($previousMonth2Revenue >= self::MIN_USER_PRICE);

            return [
                'id' => $userId,
                'name' => $userRow['name'],
                'total_price' => $currentMonthRevenue,
                'title' => $isQualified ? self::STATUS_QUALIFIED : self::STATUS_NOT_QUALIFIED,
            ];
        });

        // Tính toán quỹ thưởng và phân bổ
        $qualifiedUsers = $usersCollection->filter(fn($user) => $user['title'] === self::STATUS_QUALIFIED);
        $bonusPool = $totalRevenue * self::DISCOUNT_RATE;
        $bonusPerUser = $qualifiedUsers->count() ? ($bonusPool / $qualifiedUsers->count()) : 0.0;

        // Cập nhật thông tin bonus cho từng user
        $usersWithBonus = $usersCollection->map(function (array $user) use ($bonusPerUser) {
            $user['bonus'] = ($user['title'] === self::STATUS_QUALIFIED) ? $bonusPerUser : 0.0;
            return $user;
        });

        return view('order.statistics', [
            'total' => $totalRevenue,
            'users' => $usersWithBonus,
            'bonusPool' => $bonusPool,
            'bonusEach' => $bonusPerUser,
            'monthLabel' => $monthsArray[0],
        ]);
    }
}
