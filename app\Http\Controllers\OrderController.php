<?php
namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\TrinoClient;

class OrderController extends Controller
{
                                               // Hằng số đưa vào class để tránh lỗi
    private const MIN_USER_PRICE  = 5000000;   // 5e6
    private const MIN_GROUP_PRICE = 250000000; // 2.5e8
    private const DISCOUNT_PRICE  = 0.01;      // 1%

    /**
     * Thống kê tổng doanh số (dùng Trino)
     */
    public function statistics(TrinoClient $trino)
    {
        // Tháng hiện tại (T) và 2 tháng trước (T-1, T-2)
        $T  = now()->startOfMonth();
        $T1 = now()->copy()->subMonth()->startOfMonth();
        $T2 = now()->copy()->subMonths(2)->startOfMonth();

        $months = [
            $T->format('Y-m'),  // T
            $T1->format('Y-m'), // T-1
            $T2->format('Y-m'), // T-2
        ];

        // 1) Tổng doanh thu toàn hệ thống trong tháng T
        $totalSql = <<<SQL
            SELECT COALESCE(SUM(CAST(price AS DOUBLE)), 0) AS total
            FROM mysql.example1.orders
            WHERE date BETWEEN DATE '{$T->toDateString()}'
            AND DATE '{$T->copy()->endOfMonth()->toDateString()}'
        SQL;
        
        $total = (float) ($trino->query($totalSql)[0]['total'] ?? 0);

        // 2) Doanh thu cá nhân theo từng tháng (T-2 .. T)
        $monthlySql = <<<SQL
            SELECT user_id,
            date_format(CAST(date AS timestamp), '%Y-%m') AS ym,
            SUM(CAST(price AS DOUBLE)) AS total
            FROM mysql.example1.orders
            WHERE date BETWEEN DATE '{$T2->toDateString()}'
            AND DATE '{$T->copy()->endOfMonth()->toDateString()}'
            GROUP BY user_id, date_format(CAST(date AS timestamp), '%Y-%m')
        SQL;
        $monthlyRows = $trino->query($monthlySql);

        $monthlyMap = [];
        foreach ($monthlyRows as $r) {
            $uid                   = (int) $r['user_id'];
            $ym                    = (string) $r['ym'];
            $monthlyMap[$uid][$ym] = (float) $r['total'];
        }

        // 3) Lấy danh sách user
        $userSql  = "SELECT id, name FROM mysql.example1.users";
        $userRows = $trino->query($userSql);

        // 4) Map doanh thu, xét chuẩn 3 tháng liên tiếp
        $users = collect($userRows)->map(function (array $u) use ($months, $monthlyMap) {
            $uid = (int) $u['id'];

            $mT  = $monthlyMap[$uid][$months[0]] ?? 0.0;
            $mT1 = $monthlyMap[$uid][$months[1]] ?? 0.0;
            $mT2 = $monthlyMap[$uid][$months[2]] ?? 0.0;

            $ok3 = ($mT >= self::MIN_USER_PRICE)
                && ($mT1 >= self::MIN_USER_PRICE)
                && ($mT2 >= self::MIN_USER_PRICE);

            return [
                'id'          => $uid,
                'name'        => $u['name'],
                'total_price' => $mT,
                'title'       => $ok3 ? 'Đạt chuẩn' : 'Chưa đạt',
            ];
        });

        // 5) Quỹ thưởng & chia đều cho người đạt chuẩn
        $qualified = $users->filter(fn($u) => $u['title'] === 'Đạt chuẩn');
        $bonusPool = $total * self::DISCOUNT_PRICE;
        $bonusEach = $qualified->count() ? ($bonusPool / $qualified->count()) : 0.0;

        $users = $users->map(function (array $u) use ($bonusEach) {
            $u['bonus'] = ($u['title'] === 'Đạt chuẩn') ? $bonusEach : 0.0;
            return $u;
        });

        return view('order.statistics', [
            'total'      => $total,
            'users'      => $users,
            'bonusPool'  => $bonusPool,
            'bonusEach'  => $bonusEach,
            'monthLabel' => $months[0],
        ]);
    }
}
