<?php

return [
    'host'       => env('TRINO_HOST', 'localhost'),
    'port'       => env('TRINO_PORT', 8080),
    'tls'        => (bool) env('TRINO_TLS', false),
    'user'       => env('TRINO_USER', 'laravel'),
    'catalog'    => env('TRINO_CATALOG', 'tpch'),
    'schema'     => env('TRINO_SCHEMA', 'tiny'),
    'basic_user' => env('TRINO_BASIC_USER'),
    'basic_pass' => env('TRINO_BASIC_PASS'),
    'timeout'    => env('TRINO_TIMEOUT', 60),
    'max_pages'  => env('TRINO_MAX_PAGES', 500),
];
