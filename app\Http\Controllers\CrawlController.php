<?php
namespace App\Http\Controllers;

use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Symfony\Component\DomCrawler\Crawler;

class CrawlController extends Controller
{
    private $httpClient;

    public function __construct()
    {
        $this->httpClient = new Client([
            'timeout' => 30,
            'verify'  => false,
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            ],
        ]);
    }

    /**
     * Giao diện crawl
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     */
    public function index()
    {
        return view('crawl.index');
    }

    /**
     * Hiển thị nội dung bài viết
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Http\RedirectResponse
     */
    public function crawl(Request $request)
    {
        $request->validate([
            'url' => 'required|url',
        ]);

        $url = $request->input('url');

        try {
            $articleData = $this->extractArticleContent($url);
            return view('crawl.result', compact('articleData', 'url'));
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Không thể crawl URL này: ' . $e->getMessage()]);
        }
    }

    /**
     * Trích xuất nội dung từ URL
     * @param mixed $url
     * @return array{author: mixed, content: mixed, domain: array|bool|int|string|null, images: array, meta_description: mixed, published_date: mixed, title: string}
     */
    private function extractArticleContent($url)
    {
        // Lấy HTML từ URL
        $response = $this->httpClient->get($url);
        $html     = $response->getBody()->getContents();

        // Tạo Crawler instance
        $crawler = new Crawler($html);

        // Trích xuất dữ liệu
        $articleData = [
            'title'   => $this->extractTitle($crawler),
            'content' => $this->extractMainContent($crawler),
            'images'  => $this->extractImages($crawler, $url),
        ];

        return $articleData;
    }

    /**
     * Trích xuất tiêu đề
     * @param mixed $crawler
     * @return string
     */
    private function extractTitle($crawler)
    {
        // Thử các selector phổ biến cho tiêu đề
        $titleSelectors = [
            'h1',
            '.entry-title',
            '.post-title',
            '.article-title',
            '.title',
            'title',
        ];

        foreach ($titleSelectors as $selector) {
            $title = $crawler->filter($selector)->first();
            if ($title->count() > 0) {
                return trim($title->text());
            }
        }

        return 'Không tìm thấy tiêu đề';
    }

    /**
     * Trích xuất nội dung chính
     * @param mixed $crawler
     */
    private function extractMainContent($crawler)
    {
        // Loại bỏ các thành phần không mong muốn trước
        $this->removeUnwantedElements($crawler);

        // Thử các selector phổ biến cho nội dung chính
        $contentSelectors = [
            '.entry-content',
            '.post-content',
            '.article-content',
            '.content',
            '.post-body',
            '.article-body',
            'article',
            '.main-content',
            '#content',
        ];

        foreach ($contentSelectors as $selector) {
            $content = $crawler->filter($selector)->first();
            if ($content->count() > 0) {
                return $this->cleanContent($content->html());
            }
        }

        // Nếu không tìm thấy, thử tìm thẻ có nhiều text nhất
        return $this->findLargestTextBlock($crawler);
    }

    /**
     * Xóa các thành phần không mong muốn
     * @param mixed $crawler
     * @return void
     */
    private function removeUnwantedElements($crawler)
    {
        $unwantedSelectors = [
            'header',
            'footer',
            'nav',
            'aside',
            '.sidebar',
            '.header',
            '.footer',
            '.navigation',
            '.menu',
            '.ads',
            '.advertisement',
            '.ad',
            '.social-share',
            '.comments',
            '.comment',
            '.related-posts',
            '.widget',
            'script',
            'style',
            'noscript',
        ];

        foreach ($unwantedSelectors as $selector) {
            $crawler->filter($selector)->each(function ($node) {
                $domElement = $node->getNode(0);
                if ($domElement && $domElement->parentNode) {
                    $domElement->parentNode->removeChild($domElement);
                }
            });
        }
    }

    /**
     * Làm sạch nội dung
     * @param mixed $html
     * @return string
     */
    private function cleanContent($html)
    {
        // Loại bỏ các thẻ không mong muốn nhưng giữ cấu trúc
        $allowedTags = '<p><br><strong><b><em><i><u><h1><h2><h3><h4><h5><h6><ul><ol><li><blockquote><img><a>';
        $cleanHtml   = strip_tags($html, $allowedTags);

        // Loại bỏ các thuộc tính không cần thiết
        $cleanHtml = preg_replace('/(<[^>]+) style="[^"]*"/i', '$1', $cleanHtml);
        $cleanHtml = preg_replace('/(<[^>]+) class="[^"]*"/i', '$1', $cleanHtml);
        $cleanHtml = preg_replace('/(<[^>]+) id="[^"]*"/i', '$1', $cleanHtml);

        return trim($cleanHtml);
    }

    /**
     * Tìm khối text lớn nhất
     * @param mixed $crawler
     */
    private function findLargestTextBlock($crawler)
    {
        $maxLength   = 0;
        $bestContent = '';

        $crawler->filter('div, section, article')->each(function ($node) use (&$maxLength, &$bestContent) {
            $text = trim($node->text());
            if (strlen($text) > $maxLength && strlen($text) > 200) {
                $maxLength   = strlen($text);
                $bestContent = $node->html();
            }
        });

        return $bestContent ?: 'Không thể trích xuất nội dung';
    }

    /**
     *
     * Trích xuất hình ảnh
     * @param mixed $crawler
     * @param mixed $baseUrl
     * @return array{alt: mixed, src: mixed[]}
     */
    private function extractImages($crawler, $baseUrl)
    {
        $images = [];

        $crawler->filter('img')->each(function ($node) use (&$images, $baseUrl) {
            $src = $node->attr('src');
            $alt = $node->attr('alt') ?: '';

            if ($src) {
                // Chuyển đổi relative URL thành absolute URL
                if (strpos($src, 'http') !== 0) {
                    $src = $this->makeAbsoluteUrl($src, $baseUrl);
                }

                $images[] = [
                    'src' => $src,
                    'alt' => $alt,
                ];
            }
        });

        return $images;
    }

    /**
     * Chuyển đổi URL tương đối thành URL tuyệt đối
     * @param mixed $relativeUrl
     * @param mixed $baseUrl
     * @return string
     */
    private function makeAbsoluteUrl($relativeUrl, $baseUrl)
    {
        if (strpos($relativeUrl, '//') === 0) {
            return 'https:' . $relativeUrl;
        }

        if (strpos($relativeUrl, '/') === 0) {
            $parsedUrl = parse_url($baseUrl);
            return $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . $relativeUrl;
        }

        return rtrim($baseUrl, '/') . '/' . ltrim($relativeUrl, '/');
    }

    /**
     * Phương thức crawl nâng cao với xử lý lỗi tốt hơn
     */
    public function advancedCrawl(Request $request)
    {
        $request->validate([
            'url' => 'required|url',
        ]);

        $url = $request->input('url');

        try {
            // Kiểm tra xem URL có thể truy cập được không
            $response    = $this->httpClient->head($url);
            $contentType = $response->getHeader('Content-Type')[0] ?? '';

            if (strpos($contentType, 'text/html') === false) {
                throw new \Exception('URL này không phải là trang HTML');
            }

            $articleData = $this->extractArticleContent($url);

            // Kiểm tra chất lượng nội dung
            if (strlen(strip_tags($articleData['content'])) < 100) {
                $articleData['warning'] = 'Nội dung có vẻ quá ngắn, có thể không chính xác';
            }

            return view('crawl.result', compact('articleData', 'url'));

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            return back()->withErrors(['error' => 'Không thể truy cập URL: ' . $e->getMessage()]);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Lỗi crawl: ' . $e->getMessage()]);
        }
    }
}
